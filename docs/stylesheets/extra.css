/* Custom styles for white header and tabs with black text */

/* Header background and text color */
.md-header {
  background-color: white !important;
  color: black !important;
}

/* Header title/site name color */
.md-header__title {
  color: black !important;
}

/* Header navigation links */
.md-header-nav__title {
  color: black !important;
}

/* Header buttons and icons */
.md-header__button {
  color: black !important;
}

.md-header__option {
  color: black !important;
}

/* Search button in header */
.md-search__input {
  background-color: rgba(0, 0, 0, 0.1) !important;
  color: black !important;
}

.md-search__input::placeholder {
  color: rgba(0, 0, 0, 0.6) !important;
}

/* Navigation tabs background and text */
.md-tabs {
  background-color: white !important;
  color: black !important;
}

/* Individual tab styling */
.md-tabs__item {
  color: black !important;
}

.md-tabs__link {
  color: black !important;
}

/* Active tab styling */
.md-tabs__link--active {
  color: black !important;
  font-weight: bold;
}

/* Hover effects for tabs */
.md-tabs__link:hover {
  color: #333 !important;
  background-color: rgba(0, 0, 0, 0.05) !important;
}

/* Repository link in header */
.md-source {
  color: black !important;
}

.md-source__repository {
  color: black !important;
}

/* Color theme toggle button */
.md-header__option input + label {
  color: black !important;
}

/* Ensure proper contrast for any other header elements */
.md-header * {
  border-color: rgba(0, 0, 0, 0.2) !important;
}

/* Optional: Add a subtle border at the bottom of the header for better separation */
.md-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
}
