# Project information
site_name: PepsiCo CloudTechDoc
site_url: https://cloudtechdoc.mypepsico.com/
site_author: PepsiCo CSA Team
site_description: >-
  PepsiCo Cloud Computing Knowledge Center.

# Repository
repo_name: PepsiCoIT/Cloud_Solution_Architecture
repo_url: https://dev.azure.com/PepsiCoIT/Cloud_Solution_Architecture/

# Copyright
copyright: Copyright &copy; 2023-2025 PepsiCo

# Configuration
theme:
  name: material
  custom_dir: material/overrides
  features:
    - announce.dismiss
    - content.action.edit
    - content.action.view
    - content.code.annotate
    - content.code.copy
    # - content.code.select
    # - content.footnote.tooltips
    # - content.tabs.link
    - content.tooltips
    # - header.autohide
    # - navigation.expand
    - navigation.footer
    - navigation.indexes
    # - navigation.instant
    # - navigation.instant.prefetch
    # - enabled by Derek
    - navigation.instant.progress
    # - navigation.instant.progress
    # - navigation.prune
    - navigation.sections
    - navigation.tabs
    # - navigation.tabs.sticky
    - navigation.top
    - navigation.tracking
    - search.highlight
    - search.share
    - search.suggest
    - toc.follow
    # - toc.integrate
  palette:
    - media: "(prefers-color-scheme)"
      toggle:
        icon: material/link
        name: Switch to light mode
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: indigo
      accent: indigo
      toggle:
        icon: material/toggle-switch
        name: Switch to dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: black
      accent: indigo
      toggle:
        icon: material/toggle-switch-off
        name: Switch to system preference
  font:
    text: Roboto
    code: Roboto Mono
  favicon: assets/favicon.png
  icon:
    logo: logo

# Plugins
plugins:
  - blog
  - search:
      separator: '[\s\u200b\-_,:!=\[\]()"`/]+|\.(?!\d)|&[lg]t;|(?!\b)(?=[A-Z][a-z])'
  - minify:
      minify_html: true
  - multirepo:
      cleanup: false
  - glightbox
  - git-revision-date-localized:
      enable_creation_date: true
      type: timeago

# Hooks
hooks:
  - material/overrides/hooks/shortcodes.py
  # - material/overrides/hooks/translations.py  # Disabled - only needed for MkDocs Material development

# Additional configuration
extra:
  status:
    new: Recently added
    deprecated: Deprecated
  analytics:
    provider: google
    property: !ENV GOOGLE_ANALYTICS_KEY
  generator: false
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/squidfunk
    - icon: fontawesome/brands/docker
      link: https://hub.docker.com/r/squidfunk/mkdocs-material/
    - icon: fontawesome/brands/python
      link: https://pypi.org/project/mkdocs-material/
    - icon: fontawesome/brands/bluesky
      link: https://bsky.app/profile/squidfunk.bsky.social
    - icon: fontawesome/brands/mastodon
      link: https://fosstodon.org/@squidfunk
    - icon: fontawesome/brands/x-twitter
      link: https://x.com/squidfunk

# Extensions
markdown_extensions:
  - abbr
  - admonition
  - attr_list
  - def_list
  - footnotes
  - md_in_html
  - toc:
      permalink: true
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.betterem:
      smart_enable: all
  - pymdownx.caret
  - pymdownx.details
  - pymdownx.emoji:
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
      emoji_index: !!python/name:material.extensions.emoji.twemoji
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.keys
  - pymdownx.magiclink:
      normalize_issue_symbols: true
      repo_url_shorthand: true
      user: squidfunk
      repo: mkdocs-material
  - pymdownx.mark
  - pymdownx.smartsymbols
  - pymdownx.snippets:
      auto_append:
        - includes/mkdocs.md
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.tabbed:
      alternate_style: true
      combine_header_slug: true
      slugify: !!python/object/apply:pymdownx.slugs.slugify
        kwds:
          case: lower
  - pymdownx.tasklist:
      custom_checkbox: true
  - pymdownx.tilde

not_in_nav: |
  /tutorials/**/*.md

# Page tree
nav:
  - Home: index.md
  - Cloud Foundation Design: '!import https://#{System.AccessToken}#@dev.azure.com/pepsiCoIT/Cloud_Solution_Architecture/_git/Cloud-Foundation-Documents?branch=main'  
  - Getting started:
    - Installation: getting-started.md
    - Creating your site: creating-your-site.md
    - Publishing your site: publishing-your-site.md
    - Customization: customization.md
    - Conventions: conventions.md
    - Browser support: browser-support.md
    - Enterprise feedback: enterprise-support.md
    - Philosophy: philosophy.md
    - Alternatives: alternatives.md
    - License: license.md
    - Tutorials:
      - tutorials/index.md
      - "Blogs":
        - tutorials/blogs/basic.md
        - tutorials/blogs/navigation.md
        - tutorials/blogs/engage.md
      - "Social cards":
        - tutorials/social/basic.md
        - tutorials/social/custom.md
    - Changelog:
      - changelog/index.md
      - How to upgrade: upgrade.md
  - Setup:
    - setup/index.md
    - Changing the colors: setup/changing-the-colors.md
    - Changing the fonts: setup/changing-the-fonts.md
    - Changing the language: setup/changing-the-language.md
    - Changing the logo and icons: setup/changing-the-logo-and-icons.md
    - Ensuring data privacy: setup/ensuring-data-privacy.md
    - Setting up navigation: setup/setting-up-navigation.md
    - Setting up site search: setup/setting-up-site-search.md
    - Setting up site analytics: setup/setting-up-site-analytics.md
    - Setting up social cards: setup/setting-up-social-cards.md
    - Setting up a blog: setup/setting-up-a-blog.md
    - Setting up tags: setup/setting-up-tags.md
    - Setting up versioning: setup/setting-up-versioning.md
    - Setting up the header: setup/setting-up-the-header.md
    - Setting up the footer: setup/setting-up-the-footer.md
    - Adding a git repository: setup/adding-a-git-repository.md
    - Adding a comment system: setup/adding-a-comment-system.md
    - Building an optimized site: setup/building-an-optimized-site.md
    - Building for offline usage: setup/building-for-offline-usage.md
    - Extensions:
      - setup/extensions/index.md
      - Python Markdown: setup/extensions/python-markdown.md
      - Python Markdown Extensions: setup/extensions/python-markdown-extensions.md
  - Plugins:
    - plugins/index.md
    - Blog: plugins/blog.md
    - Group: plugins/group.md
    - Info: plugins/info.md
    - Meta: plugins/meta.md
    - Offline: plugins/offline.md
    - Optimize: plugins/optimize.md
    - Privacy: plugins/privacy.md
    - Projects: plugins/projects.md
    - Search: plugins/search.md
    - Social: plugins/social.md
    - Tags: plugins/tags.md
    - Typeset: plugins/typeset.md
    - Requirements:
      - Image processing: plugins/requirements/image-processing.md
      - Caching: plugins/requirements/caching.md
  - Reference:
    - reference/index.md
    - Admonitions: reference/admonitions.md
    - Annotations: reference/annotations.md
    - Buttons: reference/buttons.md
    - Code blocks: reference/code-blocks.md
    - Content tabs: reference/content-tabs.md
    - Data tables: reference/data-tables.md
    - Diagrams: reference/diagrams.md
    - Footnotes: reference/footnotes.md
    - Formatting: reference/formatting.md
    - Grids: reference/grids.md
    - Icons, Emojis: reference/icons-emojis.md
    - Images: reference/images.md
    - Lists: reference/lists.md
    - Math: reference/math.md
    - Tooltips: reference/tooltips.md
  - Insiders:
    - insiders/index.md
    - Why sponsor us: insiders/why-sponsor-us.md
    - What's in it for you: insiders/benefits.md
    - Who is sponsoring: insiders/our-sponsors.md
    - Sponsoring tiers: insiders/sponsoring-tiers.md
    - How to sponsor: insiders/how-to-sponsor.md
    - Additional information:
      - Payment and billing: insiders/payment-and-billing.md
      - Access management: insiders/access-management.md
      - Runtime and cancellation: insiders/runtime-and-cancellation.md
      - Privacy: insiders/privacy.md
      - License: insiders/license.md
      - Support: support.md
    - Using Insiders:
      - Getting started: insiders/getting-started.md
    - Changelog:
      - insiders/changelog/index.md
      - How to upgrade: insiders/upgrade.md
  - Community:
    - Contributing:
      - contributing/index.md
      - Reporting a bug: contributing/reporting-a-bug.md
      - Reporting a docs issue: contributing/reporting-a-docs-issue.md
      - Requesting a change: contributing/requesting-a-change.md
      - Adding translations: contributing/adding-translations.md
      - Making a pull request: contributing/making-a-pull-request.md
      - Asking a question: https://github.com/squidfunk/mkdocs-material/discussions
    - Guides:
      - Creating a reproduction: guides/creating-a-reproduction.md
    - Community experts program:
      - insiders/community-experts-program/index.md
  - Blog:
    - blog/index.md



# site_name: PepsiCo Cloud Computing Knowledge Center
# site_description: PepsiCo Cloud Computing Knowledge Center
# site_url: https://mypepsico.com/cloud

# theme:
#   name: material  
#   logo: images/pepsicologo.png
#   favicon: images/pepsicologo.png  
#   custom_dir: overrides
#   language: en  # Moved under theme
#   icon:
#     logo: fontawesome/brands/git-square
#   palette:
#     - scheme: default
#       primary: black
#       accent: indigo
#       toggle:
#         icon: material/toggle-switch
#         name: Switch to dark mode
#     - scheme: slate
#       primary: black
#       accent: indigo
#       toggle:
#         icon: material/toggle-switch-off-outline
#         name: Switch to light mode
#   features:  # Consolidated features under theme
#     - announce.dismiss
#     - content.code.annotate
#     - navigation.tabs  
#     - navigation.footer 
#     - navigation.instant
#     - navigation.tracking
#     - navigation.tabs.sticky
#     - navigation.top
#     - navigation.indexes
#     - navigation.sections
#     - search.suggest
#     - search.highlight
#     - search.share
#     - toc.follow
#   font:  # Moved under theme
#     text: Roboto
#     code: Roboto Mono
  
# extra_css:
#   - stylesheets/extra.css

# repo_name: ado-git-repo
# repo_url: "https://dev.azure.com/PepsiCoIT/Cloud_Solution_Architecture/_git/Cloud-Arch-Documents"
# edit_uri: "https://dev.azure.com/PepsiCoIT/Cloud_Solution_Architecture/_git/Cloud-Arch-Documents"

# plugins:
#   - multirepo:
#       cleanup: false
#   - search
#   # Removed tags plugin since it's not installed
#   - glightbox
#   - git-revision-date-localized:
#       enable_creation_date: true
#       type: timeago

# markdown_extensions:
#   - abbr
#   - attr_list
#   - def_list
#   - footnotes
#   - md_in_html
#   - meta
#   - admonition
#   - tables
#   - toc:
#       permalink: true
#       toc_depth: 5
#   - pymdownx.betterem
#   - pymdownx.details
#   - pymdownx.critic
#   - pymdownx.caret
#   - pymdownx.keys
#   - pymdownx.mark
#   - pymdownx.tilde
#   - pymdownx.superfences
#   - pymdownx.highlight
#   - pymdownx.inlinehilite
#   - pymdownx.snippets
#   - pymdownx.tabbed:
#       alternate_style: true
#   - pymdownx.highlight:
#       anchor_linenums: true
#   - pymdownx.tasklist:
#       custom_checkbox: true
#   - pymdownx.superfences:
#       custom_fences:
#         - name: mermaid
#           class: mermaid
#           format: !!python/name:pymdownx.superfences.fence_code_format
#   - pymdownx.emoji:  # Updated to use new Material emoji extensions
#       emoji_index: !!python/name:material.extensions.emoji.twemoji
#       emoji_generator: !!python/name:material.extensions.emoji.to_svg

# extra:
#   generator: false
#   social:
#     - icon: CAVO-Logo
#       link: https://cloudtechdoc.mypepsico.com/
#       name: PepsiCo Cloud Computing Knowledge Center

# copyright: Copyright &copy; 2023-2025 PepsiCo

# # Page tree
# nav:
#   - Getting Started: 
#     - getting-started/getting-started.md
#     - getting-started/onboarding-e2e.md
#     - getting-started/learning.md
#   - Reference Implementations: '!import https://#{System.AccessToken}#@dev.azure.com/PepsiCoIT/Cloud_Solution_Architecture/_git/Cloud-Solution-Architecture?branch=main'
#   - Cloud Foundation Design: '!import https://#{System.AccessToken}#@dev.azure.com/pepsiCoIT/Cloud_Solution_Architecture/_git/Cloud-Foundation-Documents?branch=main'
#   - Cloud Architecture Artifacts: 
#     - Cloud Architecture Drawings Requirements: 
#       - Checklist: arch-artifacts/checklist.md
#     - Architecture Templates: arch-artifacts/templates.md
#   - Blog:
#     - blog/index.md
#     - 2023:
#         - blog/2023/the-past-present-and-future.md
#         - blog/2023/excluding-content-from-search.md
#         - blog/2023/search-better-faster-smaller.md
#   - FAQ: faq/index.md
